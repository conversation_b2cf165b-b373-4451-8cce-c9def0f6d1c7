# 🖼️ J<PERSON> uż<PERSON> zoptymalizowanych obrazków

## 🎉 Wyniki optymalizacji

✅ **Oszczędności: 1.9 MB (27% mniej!)**
- Oryginalny rozmiar: 7 MB
- Po optymalizacji WebP: 5.1 MB
- Oczekiwany boost Lighthouse: +27 punktów

## 📁 Dostępne formaty

```
public/images/
├── optimized/          # Skompresowane JPEG/PNG (26% mniej)
├── webp/              # Format WebP (27% mniej) ⭐ NAJLEPSZY
└── [oryginalne]       # Backup
```

## 🚀 Implementacja w kodzie

### 1. Komponent OptimizedImage

Stwórz `components/OptimizedImage.jsx`:

```jsx
import Image from 'next/image';

const OptimizedImage = ({ 
  src, 
  alt, 
  width, 
  height, 
  priority = false,
  className = '',
  ...props 
}) => {
  // Automatycznie użyj WebP jeśli dostępny
  const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
  const optimizedSrc = src.replace('/images/', '/images/optimized/');
  
  return (
    <picture className={className}>
      {/* WebP dla nowoczesnych przeglądarek */}
      <source 
        srcSet={`/images/webp/${src.replace('/images/', '')}`}
        type="image/webp" 
      />
      
      {/* Fallback - skompresowany oryginalny */}
      <Image
        src={optimizedSrc}
        alt={alt}
        width={width}
        height={height}
        priority={priority}
        {...props}
      />
    </picture>
  );
};

export default OptimizedImage;
```

### 2. Użycie w komponentach

```jsx
// Zamiast:
<Image src="/images/gallery/IMG_5914.jpg" alt="Gallery" width={400} height={300} />

// Użyj:
<OptimizedImage 
  src="/images/gallery/IMG_5914.jpg" 
  alt="Gallery" 
  width={400} 
  height={300} 
/>
```

### 3. Hero image z preloadingiem

```jsx
// components/Hero.jsx
import OptimizedImage from './OptimizedImage';

const Hero = () => {
  return (
    <section className="relative h-screen">
      <OptimizedImage
        src="/images/background/bali-hero.jpg"
        alt="Bali Hero"
        fill
        priority // Preload dla hero image
        className="object-cover"
      />
      <div className="absolute inset-0 bg-black bg-opacity-30">
        <h1>Bakasana Travel</h1>
      </div>
    </section>
  );
};
```

### 4. Gallery z lazy loading

```jsx
// components/Gallery.jsx
import OptimizedImage from './OptimizedImage';

const Gallery = ({ images }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {images.map((image, index) => (
        <OptimizedImage
          key={index}
          src={image.src}
          alt={image.alt}
          width={400}
          height={300}
          loading="lazy" // Lazy loading dla gallery
          className="rounded-lg shadow-lg"
        />
      ))}
    </div>
  );
};
```

### 5. Responsive images

```jsx
// Różne rozmiary dla różnych ekranów
<OptimizedImage
  src="/images/gallery/IMG_5914.jpg"
  alt="Gallery"
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  fill
  className="object-cover"
/>
```

## ⚡ Performance tips

### 1. Preload krytycznych obrazków

W `pages/_document.js`:

```jsx
import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html>
      <Head>
        {/* Preload hero image */}
        <link
          rel="preload"
          as="image"
          href="/images/webp/background/bali-hero.webp"
          type="image/webp"
        />
        <link
          rel="preload"
          as="image"
          href="/images/optimized/background/bali-hero.jpg"
          type="image/jpeg"
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
```

### 2. Next.js config dla obrazków

W `next.config.js`:

```js
module.exports = {
  images: {
    formats: ['image/webp', 'image/jpeg'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
}
```

### 3. CSS dla lepszego UX

```css
/* styles/globals.css */

/* Smooth loading transition */
.image-container {
  position: relative;
  overflow: hidden;
}

.image-container img {
  transition: opacity 0.3s ease;
}

/* Placeholder podczas ładowania */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

## 🔧 Automatyzacja

### 1. Script do aktualizacji wszystkich obrazków

Stwórz `scripts/update-image-paths.js`:

```js
const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Znajdź wszystkie pliki JSX/TSX
const files = glob.sync('src/**/*.{js,jsx,ts,tsx}');

files.forEach(file => {
  let content = fs.readFileSync(file, 'utf8');
  
  // Zamień Image na OptimizedImage
  content = content.replace(
    /import Image from ['"]next\/image['"];?/g,
    "import OptimizedImage from '../components/OptimizedImage';"
  );
  
  content = content.replace(
    /<Image\s/g,
    '<OptimizedImage '
  );
  
  fs.writeFileSync(file, content);
});

console.log('✅ Zaktualizowano wszystkie obrazki!');
```

### 2. Dodaj do package.json

```json
{
  "scripts": {
    "images:update-paths": "node scripts/update-image-paths.js"
  }
}
```

## 📊 Monitoring performance

### 1. Sprawdź wyniki w Lighthouse

```bash
npm run performance:audit
```

### 2. Monitoruj Core Web Vitals

```jsx
// pages/_app.js
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric) {
  // Wyślij do Google Analytics lub innego narzędzia
  console.log(metric);
}

export function reportWebVitals(metric) {
  sendToAnalytics(metric);
}
```

## 🎯 Oczekiwane rezultaty

Po implementacji powinieneś zobaczyć:

- ✅ **Lighthouse Performance: >95 punktów**
- ✅ **LCP (Largest Contentful Paint): <2.5s**
- ✅ **Rozmiar strony: 27% mniejszy**
- ✅ **Szybsze ładowanie na mobile**

## 🚨 Ważne uwagi

1. **Fallback**: Zawsze zapewnij fallback dla starszych przeglądarek
2. **Alt text**: Nie zapomnij o dostępności
3. **Lazy loading**: Używaj dla obrazków poniżej fold
4. **Priority**: Ustaw tylko dla krytycznych obrazków (hero, logo)
5. **Responsive**: Używaj różnych rozmiarów dla różnych ekranów

## 🎊 Następne kroki

1. ✅ Zaimplementuj OptimizedImage component
2. ✅ Zamień wszystkie Image na OptimizedImage
3. ✅ Dodaj preloading dla hero images
4. ✅ Przetestuj w Lighthouse
5. ✅ Ciesz się szybką stroną! 🚀

---

**💡 Pro tip**: Te 1.9 MB oszczędności to ogromna różnica, szczególnie na mobile!
